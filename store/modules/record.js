const state = {
  records: []
}

const getters = {
  records: state => state.records
}

const mutations = {
  setRecords(state, records) {
    state.records = records
  }
}

const actions = {
  setRecords({ commit }, records) {
    commit('setRecords', records)
  },
  getRecords({ commit }) {
    // TODO: get records from server
    return new Promise((resolve, reject) => {
      uni.$u.http.post('/user/getUserInfo',{}, {
        header: { token: token }
      }).then(res => {
        commit('setRecords', res)
        commit('setToken', res.token)
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  }
}

export default {
  state,
  getters,
  mutations,
  actions
}