const state = {
    cardInfo: {
        "id": 0,
        "user_id": 0,
        "platform": "",
        "money": "",
        "image": "",
        "card_num": "",
        "sub_bank": "",
        "name": "0"
    }
}

const mutations = {
    setCardInfo(state, cardInfo) {
        state.cardInfo = cardInfo
    }
}

const getters = {
    cardInfo: state => state.cardInfo
}

const actions = {
    setCardInfo({ commit }, cardInfo) {
        commit('setCardInfo', cardInfo)
    },
    getCardInfo({ commit }) {
        // TODO: get cardInfo from server
        return new Promise((resolve, reject) => {
            uni.$u.http.post('/card/info').then(res => {
                commit('setCardInfo', res)
                resolve(res)
            }).catch(err => {
                reject(err)
            })
        })
    }
}


export default {
    state,
    mutations,
    actions,
    getters
}