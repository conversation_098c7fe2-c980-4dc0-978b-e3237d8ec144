
const state = {
  token: '' || uni.getStorageSync('token'),
  userInfo: null || uni.getStorageSync('userInfo'),
  isLogin:false
}

const mutations = {

  setToken(state, token) {
    if (token) {
      state.token = token
      uni.setStorageSync('token', token)
    }
    else{
      state.token = ''
      uni.removeStorageSync('token')
    }
  },

  setUserInfo(state, userInfo) {
    state.userInfo = userInfo
    uni.setStorageSync('userInfo', userInfo)
  },

  setIsLogin(state, isLogin) {
    state.isLogin = isLogin
  }

}

const actions = {

  setToken({ commit }, token) {
      commit('setToken', token)
  },

  setUserInfo({ commit }, userInfo) {
    commit('setUserInfo', userInfo)
  },

  logOut({ commit }) {
    commit('setToken', '')
  },

  getUserInfo({ commit }, token = '') {
    
    if (!token) {
      token = uni.getStorageSync('token')
    }

    return new Promise((resolve, reject) => {
      uni.$u.http.post('/user/getUserInfo',{}, {
        header: { token: token }
      }).then(res => {
        commit('setUserInfo', res)
        commit('setToken', res.token)
        commit('setIsLogin', true)
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })

  }
}

const getters = {
  token: state => state.token,
  userInfo: state => state.userInfo,
  isLogin: state => state.isLogin
}

export default {
  state,
  mutations,
  actions,
  getters
}