<template>
	<view class="tabs-container">
		<view 
			class="tab tab1" 
			:class="{ active: activeTab === 1 }"
			@tap="handleTabClick(1)"
		></view>
		<view 
			class="tab tab2" 
			:class="{ active: activeTab === 2 }"
			@tap="handleTabClick(2)"
		></view>
		<view 
			class="tab tab3" 
			:class="{ active: activeTab === 3 }"
			@tap="handleTabClick(3)"
		></view>
		<view 
			class="tab tab4" 
			:class="{ active: activeTab === 4 }"
			@tap="handleTabClick(4)"
		></view>
	</view>
</template>

<script>
export default {
	name: 'jy-index-tabs',
	props: {
		// 当前激活的tab索引
		activeTab: {
			type: Number,
			default: 1
		}
	},
	methods: {
		handleTabClick(tabIndex) {
			// 触发tab切换事件，传递选中的tab索引
			this.$emit('tab-change', tabIndex);
		}
	}
}
</script>

<style lang="scss" scoped>
.tabs-container {
	position: absolute;
	left: 0;
	top:0;
}

.tab {
	position: absolute;
	background: #000;
	width: 100rpx;
	height: 70rpx;
	opacity: 0.3; // 默认半透明
	transition: opacity 0.3s ease;
	
	&.active {
		opacity: 0.8; // 激活状态更明显
	}
}

.tab1 {
	top: 290rpx;
	left: 60rpx;
}

.tab2 {
	top: 290rpx;
	left: 200rpx;
}

.tab3 {
	top: 290rpx;
	left: 360rpx;
}

.tab4 {
	top: 290rpx;
	left: 510rpx;
}
</style>
