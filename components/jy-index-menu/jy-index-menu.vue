<template>
	<view class="menu-container">
		<view class="menu menu1"  @tap="handleTabClick(1)"></view>
		<view class="menu menu2"  @tap="handleTabClick(1)"></view>
		<view class="menu menu3"  @tap="handleTabClick(1)"></view>
		<view class="menu menu4"  @tap="handleTabClick(1)"></view>

		<view class="option-list">
			<view class="option"></view>
			<view class="option"></view>
			<view class="option"></view>
			<view class="option"></view>
			<view class="option"></view>
			<view class="option"></view>
			<view class="option"></view>
			<view class="option"></view>
			<view class="option"></view>
			<view class="option"></view>
		</view>

	</view>
</template>

<script>
export default {
	name: "jy-index-menu",
	data() {
		return {

		};
	}
}
</script>

<style lang="scss">
.menu-container {
	position: absolute;
	left: 0;
	top: 0;

	.menu{
		position: absolute;
		background: #000;
		width: 100rpx;
		height: 70rpx;
		opacity: 0.3; // 默认半透明
		transition: opacity 0.3s ease;
	}

	.menu1{
		top:400rpx;
		left: 44rpx;
		width: 150rpx;
		height: 190rpx;
	}

	.menu2{
		top:400rpx;
		left: 214rpx;
		width: 150rpx;
		height: 190rpx;
	}

	.menu3{
		top:400rpx;
		left: 384rpx;
		width: 150rpx;
		height: 190rpx;
	}

	.menu4{
		top:400rpx;
		left: 554rpx;
		width: 150rpx;
		height: 190rpx;
	}

	.option-list{
		position: absolute;
		display: grid;
		// 一行五个
		grid-template-columns: repeat(5, 1fr);
		width: 690rpx;
		padding: 0 30rpx;
		left: 0;
		top: 600rpx;
		// 居中
		justify-content: center;
		gap: 20rpx;

		.option{
			height: 120rpx;
			background: #000;
			opacity: 0.3; // 默认半透明
			transition: opacity 0.3s ease;
			// 居中
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

}


</style>