<template>
	<view style="position: relative;display: block;">
		<image :src="src" mode="widthFix" style="width: 100%;display: block;"></image>
		<slot></slot>
	</view>
</template>

<script>
	export default {
		name:"jy-image-bg",
		props:{
			src:{
				type:String,
				default:"../../static/image/index/index-1.jpg"
			}
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss">

</style>