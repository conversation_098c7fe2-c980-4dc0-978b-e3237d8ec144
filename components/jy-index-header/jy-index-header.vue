<template>
	<view>

		<view class="jy-index-header" :style="headerStyle">
			
			<!-- 导航栏内容 -->
			<view class="nav-bar" :style="navBarStyle">
				<!-- 左侧内容 -->
				<view class="nav-left">
					<view class="location-info" v-if="showLocationInfo">
						<u-icon name="map" size="28" color="#333"></u-icon>
						<text class="location-text">郑州</text>
					</view>
					<text class="login-text" v-else>登录</text>
				</view>

				<!-- 中间搜索框 -->
				<view class="nav-center">
					<view class="search-box">
						<u-icon name="scan" size="32" color="#999" class="scan-icon"></u-icon>
						<view class="search-text-container">
							<jy-text-swiper :texts="searchTexts" :interval="3000" :duration="500"
								:text-style="{ fontSize: '26rpx', color: '#999' }"
								@change="onTextChange"></jy-text-swiper>
						</view>
						<u-icon name="scan" size="32" color="#999" class="scan-icon"></u-icon>
					</view>
				</view>

				<!-- 右侧图标组 -->
				<view class="nav-right">
					<view class="icon-item">
						<u-icon name="bell-fill" size="40" color="#333"></u-icon>
						<view class="red-dot"></view>
					</view>
					<view class="icon-item">
						<u-icon name="chat-fill" size="40" color="#333"></u-icon>
					</view>
					<view class="icon-item">
						<u-icon name="more-circle-fill" size="40" color="#333"></u-icon>
					</view>
				</view>
			</view>

			<!-- 选项按钮区域 -->
			<view class="options-bar" v-if="showOptionsBar" :style="optionsBarStyle">
				<view class="option-item" v-for="(option, index) in options" :key="index"
					:class="{ 'active': index === activeIndex }"
					@tap="handleOptionClick(index)">
					<text class="option-text">{{ option }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: "jy-index-header",
	props: {
		scrollHeight: {
			type: Number,
			default: 0
		},
		// 当前激活的选项索引
		activeIndex: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			statusBarHeight: 0,
			searchTexts: [
				'随用随取 天天至',
				'分期付款账单无忧',
				'信用卡还款优惠',
				'理财产品收益高'
			],
			options: ['常用', '消费', '私银', '兴农通'],
			currentText: '随用随取 天天至'
		};
	},
	computed: {
		// 根据滚动高度计算背景透明度 - 使用更平滑的渐变算法
		backgroundOpacity() {
			const threshold = 150; // 增加透明度变化的阈值，让过渡更平滑
			if (this.scrollHeight <= 0) return 0;
			if (this.scrollHeight >= threshold) return 0.95; // 最大透明度设为0.95，保持一点透明感

			// 使用缓动函数实现更自然的渐变
			const progress = this.scrollHeight / threshold;
			// 使用 easeOutQuart 缓动函数
			const easedProgress = 1 - Math.pow(1 - progress, 4);
			return easedProgress * 0.95;
		},
		// 动态计算header样式 - 实现渐变背景
		headerStyle() {
			const opacity = this.backgroundOpacity;

			// 创建从透明到白色的渐变背景
			let backgroundColor;
			if (opacity === 0) {
				backgroundColor = 'transparent';
			} else if (opacity < 0.3) {
				// 早期阶段：从透明到淡淡的白色
				backgroundColor = `rgba(255, 255, 255, ${opacity * 0.5})`;
			} else if (opacity < 0.7) {
				// 中期阶段：逐渐增加白色
				backgroundColor = `rgba(255, 255, 255, ${0.15 + (opacity - 0.3) * 1.5})`;
			} else {
				// 后期阶段：接近完全白色
				backgroundColor = `rgba(255, 255, 255, ${0.75 + (opacity - 0.7) * 0.67})`;
			}

			return {
				backgroundColor,
				backdropFilter: opacity > 0.1 ? `blur(${opacity * 10}px)` : 'none', // 添加模糊效果
				transition: 'all 0.2s ease-out', // 更快的响应速度
				boxShadow: opacity > 0.5 ? `0 2rpx 8rpx rgba(0, 0, 0, ${opacity * 0.1})` : 'none' // 添加阴影
			};
		},
		// 是否显示地址信息
		showLocationInfo() {
			return this.backgroundOpacity >= 0.4;
		},
		// 是否显示选项栏
		showOptionsBar() {
			return this.backgroundOpacity >= 0.6;
		},
		// 导航栏的动态样式
		navBarStyle() {
			const opacity = this.backgroundOpacity;

			// 创建与header相同的渐变背景
			let backgroundColor;
			if (opacity === 0) {
				backgroundColor = 'transparent';
			} else if (opacity < 0.3) {
				backgroundColor = `rgba(255, 255, 255, ${opacity * 0.5})`;
			} else if (opacity < 0.7) {
				backgroundColor = `rgba(255, 255, 255, ${0.15 + (opacity - 0.3) * 1.5})`;
			} else {
				backgroundColor = `rgba(255, 255, 255, ${0.75 + (opacity - 0.7) * 0.67})`;
			}

			return {
				backgroundColor,
				transition: 'background-color 0.2s ease-out'
			};
		},
		// 选项栏的动态样式
		optionsBarStyle() {
			const opacity = this.backgroundOpacity;
			const showOpacity = Math.max(0, (opacity - 0.6) / 0.3); // 从0.6开始显示，到0.9完全显示

			return {
				backgroundColor: `rgba(255, 255, 255, ${0.9 + showOpacity * 0.1})`,
				opacity: showOpacity,
				transform: `translateY(${(1 - showOpacity) * -20}rpx)`,
				transition: 'all 0.3s ease-out'
			};
		}
	},
	mounted() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 0;
	},
	methods: {
		// 处理文字切换事件
		onTextChange(e) {
			this.currentText = e.text;
			// 可以在这里添加其他逻辑，比如统计或日志
		},
		// 处理选项点击事件
		handleOptionClick(index) {
			// 向父组件发送选项点击事件，传递选中的索引
			this.$emit('option-change', index);
		}
	}
}
</script>

<style lang="scss">
.jy-index-header {

	padding: 10rpx 0;
	padding-top: var(--status-bar-height);
	position: fixed;
	top: 0;
	left: 0;
	z-index: 999;
	width: 100%;

	.nav-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;

		.nav-left {

			.login-text {
				font-size: 32rpx;
				color: #333;
				font-weight: 400;
			}

			.location-info {
				display: flex;
				align-items: center;

				.location-text {
					margin-left: 8rpx;
					font-size: 32rpx;
					color: #333;
					font-weight: 500;
				}
			}
		}

		.nav-center {
			flex: 1;
			margin: 0 20rpx;

			.search-box {
				display: flex;
				align-items: center;
				height: 60rpx;
				background: #f8f8f8;
				border-radius: 30rpx;
				padding: 0 20rpx;
				position: relative;

				.scan-icon {
					flex-shrink: 0;
				}

				.search-text-container {
					flex: 1;
					margin-left: 12rpx;
					height: 40rpx;
					overflow: hidden;
					position: relative;
				}
			}
		}

		.nav-right {
			display: flex;
			align-items: center;

			.icon-item {
				position: relative;
				margin-left: 28rpx;

				&:first-child {
					margin-left: 0;
				}

				.red-dot {
					position: absolute;
					top: -6rpx;
					right: -6rpx;
					width: 16rpx;
					height: 16rpx;
					background: #ff3b30;
					border-radius: 50%;
				}
			}
		}
	}

	// 选项栏样式
	.options-bar {
		display: flex;
		align-items: center;
		padding: 20rpx;
		padding-top: 10rpx;
		// 移除静态样式，使用动态样式

		.option-item {
			margin-right: 60rpx;
			position: relative;

			&:last-child {
				margin-right: 0;
			}

			.option-text {
				font-size: 28rpx;
				color: #666;
				font-weight: 400;
				transition: color 0.3s ease;
			}

			&.active {
				.option-text {
					color: #333;
					font-weight: 500;
				}

				&::after {
					content: '';
					position: absolute;
					bottom: -10rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 6rpx;
					background: #000;
					border-radius: 999rpx;
				}
			}
		}
	}
}
</style>