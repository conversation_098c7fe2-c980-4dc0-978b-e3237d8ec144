<template>
	<view class="msg-container">
		<view class="btn btn1"></view>
		<view class="btn btn2"></view>
	</view>
</template>

<script>
	export default {
		name:"jy-msg-btn",
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss">
.msg-container {
	position: absolute;
	left: 0;
	top: 0;

	.btn{
		position: absolute;
		background: #000;
		opacity: 0.3; // 默认半透明
		transition: opacity 0.3s ease;
	}

	.btn1{
		top:574rpx;
		left: 500rpx;
		width: 200rpx;
		height: 60rpx;
	}

	.btn2{
		top:940rpx;
		left: 500rpx;
		width: 200rpx;
		height: 60rpx;
	}
}
</style>