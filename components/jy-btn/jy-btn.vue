<template>
		<view
			class="btn"
			:style="{
				width: typeof width === 'number' ? width + 'rpx' : width,
				height: typeof height === 'number' ? height + 'rpx' : height,
				top: typeof top === 'number' ? top + 'rpx' : top,
				left: typeof left === 'number' ? left + 'rpx' : left
			}"
			@tap="handleClick"
		></view>
</template>

<script>
	export default {
		name:"jy-btn",
		props:{
			// 宽度
			width: {
				type: [String, Number],
				default: '200rpx'
			},
			// 高度
			height: {
				type: [String, Number],
				default: '60rpx'
			},
			// 距离顶部的位置
			top: {
				type: [String, Number],
				default: '0rpx'
			},
			// 距离左边的位置
			left: {
				type: [String, Number],
				default: '0rpx'
			}
		},
		data() {
			return {

			};
		},
		methods: {
			handleClick() {
				// 触发点击事件，向父组件传递点击信息
				this.$emit('click');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.btn{
		position: absolute;
		background: #000;
		opacity: 0.3; // 默认半透明
		transition: opacity 0.3s ease;
	}
</style>