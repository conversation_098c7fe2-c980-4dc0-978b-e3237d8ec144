{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__EFBCC07", "name": "jsapp", "version": {"name": "1.0.0", "code": "100"}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Push": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"autoclose": false, "waiting": false, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#f4f4f6"}, "compatible": {"ignoreVersion": true}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "distribute": {"splashscreen": {"androidStyle": "default", "android": {"hdpi": "unpackage/splash/res/drawable-hdpi/0.9.png.9.png", "xhdpi": "unpackage/splash/res/drawable-xhdpi/0.9.png.9.png", "xxhdpi": "unpackage/splash/res/drawable-xxhdpi/0.9.png.9.png"}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]}, "apple": {}, "plugins": {"push": {"unipush": {"version": "2", "offline": false, "icons": {"small": {"hdpi": "unpackage/res/drawable-hdpi/push_small.png"}}}}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "nativePlugins": {"raven-notice": {"__plugin_info__": {"name": "Raven-Android自定义本地通知", "description": "创建本地通知", "platforms": "Android", "url": "", "android_package_name": "", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {}}}}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#f4fcff", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "4.75", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"color": "#7A7E83", "selectedColor": "#203db5", "borderStyle": "rgba(0,0,0,0.4)", "backgroundColor": "#f4fcff", "iconWidth": "18px", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "static/tabbar/icon-home-unselected.png", "selectedIconPath": "static/tabbar/icon-home-selected.png"}, {"pagePath": "pages/index/card", "text": "信用卡", "iconPath": "static/tabbar/icon-card-unselected.png", "selectedIconPath": "static/tabbar/icon-card-selected.png"}, {"pagePath": "pages/index/coin", "text": "财富", "iconPath": "static/tabbar/icon-wealth-unselected.png", "selectedIconPath": "static/tabbar/icon-wealth-selected.png"}, {"pagePath": "pages/index/live", "text": "生活", "iconPath": "static/tabbar/icon-life-unselected.png", "selectedIconPath": "static/tabbar/icon-life-selected.png"}, {"pagePath": "pages/index/user", "text": "我的", "iconPath": "static/tabbar/icon-mine-unselected.png", "selectedIconPath": "static/tabbar/icon-mine-selected.png"}], "height": "50px", "child": ["lauchwebview"], "selected": 0}, "launch_path": "__uniappview.html"}}