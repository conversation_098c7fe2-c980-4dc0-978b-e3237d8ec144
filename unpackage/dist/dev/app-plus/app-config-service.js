
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/index/index","pages/index/card","pages/index/live","pages/index/coin","pages/index/user","pages/other/kefu","pages/other/msg"],"window":{"navigationBarTextStyle":"black","navigationBarBackgroundColor":"#f4f4f6","backgroundColor":"#f4f4f6","scrollIndicator":"none"},"tabBar":{"color":"#7A7E83","selectedColor":"#203db5","borderStyle":"black","backgroundColor":"#f4fcff","iconWidth":"18px","list":[{"pagePath":"pages/index/index","text":"首页","iconPath":"static/tabbar/icon-home-unselected.png","selectedIconPath":"static/tabbar/icon-home-selected.png"},{"pagePath":"pages/index/card","text":"信用卡","iconPath":"static/tabbar/icon-card-unselected.png","selectedIconPath":"static/tabbar/icon-card-selected.png"},{"pagePath":"pages/index/coin","text":"财富","iconPath":"static/tabbar/icon-wealth-unselected.png","selectedIconPath":"static/tabbar/icon-wealth-selected.png"},{"pagePath":"pages/index/live","text":"生活","iconPath":"static/tabbar/icon-life-unselected.png","selectedIconPath":"static/tabbar/icon-life-selected.png"},{"pagePath":"pages/index/user","text":"我的","iconPath":"static/tabbar/icon-mine-unselected.png","selectedIconPath":"static/tabbar/icon-mine-selected.png"}]},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"uni-app","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":false,"autoclose":true},"appname":"jsapp","compilerVersion":"4.75","entryPagePath":"pages/index/index","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/index/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"uni-app","navigationStyle":"custom"}},{"path":"/pages/index/card","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"","navigationStyle":"custom"}},{"path":"/pages/index/live","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"","navigationStyle":"custom"}},{"path":"/pages/index/coin","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"","navigationStyle":"custom"}},{"path":"/pages/index/user","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"","navigationStyle":"custom"}},{"path":"/pages/other/kefu","meta":{},"window":{"navigationBarTitleText":"","navigationStyle":"custom","scrollIndicator":"none"}},{"path":"/pages/other/msg","meta":{},"window":{"navigationBarTitleText":"","navigationStyle":"custom","scrollIndicator":"none"}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
