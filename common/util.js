

const toPage = (url) => {
    uni.navigateTo({
        url: url
    })
}

const toBack = () => {
    uni.navigateBack()
}

// 发送通知
const sendNotification = (title, content) => {

    console.log("sendNotification")
    const syczuanNotice = uni.requireNativePlugin("syczuan-notice");
    // 检查平台支持
    if (uni.requireNativePlugin) {
        syczuanNotice.send(
            {
                // 唯一通知id,用于更新、取消通知
                noticeId: 1,
                // 通知渠道id 需唯一
                channalId: "Default_id",
                // 通知渠道名称 需唯一
                channalName: "Default",
                // 通知标题
                title: "通知标题",
                // 通知内容
                content: "通知内容",
                // 通知栏附加文本
                subText: "通知栏附加文本",
                // 大图标
                largeIcon: true,
                // 小图标背景颜色
                smallColor: "#000000",
                // 大图(禁止传空字符)
                bigPicture: "网络地址、绝对路径、static/image目录下",
                // 点击是否自动关闭通知
                autoCancel: true,
                // 毫秒时间戳 默认显示当前时间
                noticeTime: true,
                // 通知优先级
                import: 0,
                // 常驻状态栏
                ongoing: false,
                // 未读通知数 0不显示
                badge: 1,
                // 延迟通知时间(单位s)
                trigger: 1,
                // 通知长文本
                bigText: "",
                // 自定义按钮
                customButton: "Custom",
                // 自定义数据
                payload: {
                    pages: "/pages/index/test",
                    type: "default",
                },
            },
            (e) => {
                console.log(e);
            }
        )
    }

    // 如果不支持原生插件或在其他平台，使用 toast 提示
    uni.showToast({
        title: title + ': ' + content,
        icon: 'none',
        duration: 3000
    });

    return false;
}


export default {
    toPage,
    toBack,
    sendNotification
}