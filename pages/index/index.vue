<template>
	<view >
		<jy-index-header :scroll-height="scrollTop"></jy-index-header>

		<view>
			<jy-image-bg src="../../static/image/index/1.png" v-if="tabActive == 1">
				<jy-index-tabs :active-tab="tabActive" @tab-change="handleTabChange"></jy-index-tabs>
				<jy-index-menu></jy-index-menu>
			</jy-image-bg>
			<jy-image-bg src="../../static/image/index/2.png" v-if="tabActive == 2">
				<jy-index-tabs :active-tab="tabActive" @tab-change="handleTabChange"></jy-index-tabs>
				<jy-msg-btn></jy-msg-btn>
			</jy-image-bg>
			<jy-image-bg src="../../static/image/index/3.png" v-if="tabActive == 3">
				<jy-index-tabs :active-tab="tabActive" @tab-change="handleTabChange"></jy-index-tabs>
			</jy-image-bg>
			<jy-image-bg src="../../static/image/index/4.png" v-if="tabActive == 4">
				<jy-index-tabs :active-tab="tabActive" @tab-change="handleTabChange"></jy-index-tabs>
			</jy-image-bg>

			<image class="img" src="@/static/image/index/index-1.jpg" v-if="tabActive == 1" mode="widthFix"></image>

			<image class="img" src="@/static/image/index/msg-1.jpg" v-if="tabActive == 2" mode="widthFix"></image>

			<image class="img" src="@/static/image/index/siyin-1.jpg" v-if="tabActive == 3" mode="widthFix"></image>

			<image class="img" src="@/static/image/index/nongye-1.jpg" v-if="tabActive == 4" mode="widthFix"></image>
		</view>
	</view> 
</template>

<script>
	export default {
		data() {
			return {
				scrollTop: 0,
				tabActive:1
			};
		},
		mounted() {
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		methods: {
			handleTabChange(tabIndex) {
				this.tabActive = tabIndex;
			},

			handleBtnClick(){
				console.log("btn1 clicked!");
				uni.showToast({
					title: 'btn1被点击了',
					icon: 'success'
				});
			}
		}
	}
</script>

<style lang="scss">
.img{
	width: 100%;
	display: block; /* 将图片设为块级元素，消除行内元素间距 */
}
</style>
