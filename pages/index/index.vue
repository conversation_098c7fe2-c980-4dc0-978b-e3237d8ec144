<template>
	<view >
		<jy-index-header :scroll-height="scrollTop"></jy-index-header>

		<view>
			<jy-image-bg src="../../static/image/index/1.png" v-if="tabActive == 1">
				<view class="tab tab1" @tap="tabActive = 1"></view>
				<view class="tab tab2" @tap="tabActive = 2"></view>
				<view class="tab tab3" @tap="tabActive = 3"></view>
				<view class="tab tab4" @tap="tabActive = 4"></view>
			</jy-image-bg>
			<jy-image-bg src="../../static/image/index/2.png" v-if="tabActive == 2">
				<view class="tab tab1" @tap="tabActive = 1"></view>
				<view class="tab tab2" @tap="tabActive = 2"></view>
				<view class="tab tab3" @tap="tabActive = 3"></view>
				<view class="tab tab4" @tap="tabActive = 4"></view>
			</jy-image-bg>
			<jy-image-bg src="../../static/image/index/3.png" v-if="tabActive == 3">
				<view class="tab tab1" @tap="tabActive = 1"></view>
				<view class="tab tab2" @tap="tabActive = 2"></view>
				<view class="tab tab3" @tap="tabActive = 3"></view>
				<view class="tab tab4" @tap="tabActive = 4"></view>
			</jy-image-bg>
			<jy-image-bg src="../../static/image/index/4.png" v-if="tabActive == 4">
				<view class="tab tab1" @tap="tabActive = 1"></view>
				<view class="tab tab2" @tap="tabActive = 2"></view>
				<view class="tab tab3" @tap="tabActive = 3"></view>
				<view class="tab tab4" @tap="tabActive = 4"></view>
			</jy-image-bg>
		</view>
	</view> 
</template>

<script>
	export default {
		data() {
			return {
				scrollTop: 0,
				tabActive:1
			};
		},
		mounted() {
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		methods: {
			
			handleBtnClick(){
				console.log("btn1 clicked!");
				uni.showToast({
					title: 'btn1被点击了',
					icon: 'success'
				});
			}
		}
	}
</script>

<style lang="scss">
	
.tab{
	position: absolute;
	background: #000;
	width: 100rpx;
	height: 70rpx;
}
	
.tab1{
	top: 290rpx;
	left: 60rpx;
}

.tab2{
	top: 290rpx;
	left: 200rpx;
}
.tab3{
	top: 290rpx;
	left: 360rpx;
}
.tab4{
	top: 290rpx;
	left: 510rpx;
}


</style>
