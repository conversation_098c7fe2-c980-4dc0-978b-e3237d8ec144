import { apiUrl, platform } from "@/env";

// 此vm参数为页面的实例，可以通过它引用vuex中的变量
module.exports = (vm) => {
	// 初始化请求配置
	uni.$u.http.setConfig((config) => {
		/* config 为默认全局配置*/
		// 判断是生产环境还是 development 环境
		config.baseURL = apiUrl

		return config
	})

	// 请求拦截
	uni.$u.http.interceptors.request.use((config) => { // 可使用async await 做异步操作
		// 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
		config.data = config.data || {}
		config.header.platform = platform // 平台标识
		// 根据custom参数中配置的是否需要token，添加对应的请求头
		// 可以在此通过vm引用vuex中的变量，具体值在vm.$store.state中
		var token = uni.getStorageSync('token')
		if (token) {
			config.header.token = token
		}

		return config
	}, config => { // 可使用async await 做异步操作
		return Promise.reject(config)
	})

	// 响应拦截
	uni.$u.http.interceptors.response.use((response) => { /* 对响应成功做点什么 可使用async await 做异步操作*/
		console.log(response)
		const data = response.data

		// 自定义参数
		if (data.code !== 1) {
			uni.$u.toast(data.msg)
			return Promise.reject(data)
		}

		return data.data === undefined ? {} : data.data
	}, (response) => {
		const data = response.data
		console.log(response)
		// 对响应错误做点什么 （statusCode !== 200）
		// 401 代表token失效，需要重新登录
		if (response.statusCode === 401) {
			console.log('token失效，需要重新登录')
			// vm.$store.dispatch('logOut')
			// 跳转登录页面
			if(uni.getStorageSync('userInfo')) {
				console.log('has userInfo')
				uni.redirectTo({
					url: '/pages/public/login'
				})
			} else {
				console.log('no userInfo')
				uni.redirectTo({
					url: '/pages/public/login1'
				})
			}
			return Promise.reject(data)
		}
		return Promise.reject(response)
	})
}
